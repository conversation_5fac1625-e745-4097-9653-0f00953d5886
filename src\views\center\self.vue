<template>
  <div>
    <div class="Self">
      <el-row>
        <el-col :span="4" style="padding: 0">
          <div class="picture">
            <el-upload
              class="avatar-uploader"
              :action="getUrl"
              :show-file-list="false"
              :data="imageData"
              name="file"
              :headers="headerUrl"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="ruleForm.logo" :src="ruleForm.logo" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <el-button><i class="el-icon-edit"></i> 编辑头像</el-button>
            </el-upload>
          </div>
        </el-col>
        <el-col :span="20" style="padding: 0">
          <div style="margin-top: 20px" class="information">
            <el-form ref="form" :model="ruleForm" label-width="120px">
              <el-form-item label="姓名:">
                <el-input v-model="ruleForm.name" placeholder="请输入姓名">
                </el-input>
              </el-form-item>

              <el-form-item label="个人简介:">
                <el-input type="textarea" v-model="ruleForm.intro"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveInfo" class="submit"
                  >保存
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script setup>
import {
  saveInvite,
  authentication,
  queryListAllMajor,
} from '@/api/center/earuser.js'
import { getPersonInf } from '@/api/expert/index.js'
import { baseUrl, uploadFiles3 } from '@/config.js'
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { userStore } from '@/stores/user.js'
const getUrl = `${baseUrl}${uploadFiles3}`
const imageData = { serviceName: 'web' }
const headerUrl = { Authorization: sessionStorage.getItem('token') }
const logo = ref('')
const id = localStorage.getItem('id')
const useUserStore = userStore()
const ruleForm = ref({
  logo: '',
  imagesFileName: '',
  imagesFileSize: '',
  majorId: '',
  name: '',
  intro: '',
})
const handleAvatarSuccess = (res, file) => {
  logo.value = URL.createObjectURL(file.raw)
  ruleForm.value.logo = res.data.url
  ruleForm.value.imagesFileName = res.data.fileName
  ruleForm.value.imagesFileSize = res.data.size
}
const beforeAvatarUpload = (file) => {
  const isLt2M = file.size / 1024 / 1024 < 10
  if (!isLt2M) {
    this.$message.error('上传头像图片大小不能超过 10MB!')
    return false
  }
  return true
}

const formCode = ref({ schoolInvitationCode: '' })
const majorData = ref([])

function changeSelect(value, boo, callback) {
  majorData.value = []
  if (!boo) {
    ruleForm.value.majorId = ''
  }
  queryListAllMajor({ tagtypeId: value }).then((res) => {
    if (res.status == 0) {
      majorData.value = res.data
      if (callback) {
        callback()
      }
    }
  })
}

function isInformation() {
  getPersonInf({ id: id }).then((res) => {
    if (res.status == 0) {
      let obj = { ...res.data }
      ruleForm.value = obj
      ruleForm.value.majorId = ''
      if (res.data.collegeId) {
        changeSelect(res.data.collegeId, true, () => {
          if (res.data.majorId == '0' || res.data.majorId == 0) {
            ruleForm.value.majorId = ''
          } else {
            ruleForm.value.majorId = res.data.majorId
          }
        })
      }
      formCode.value.schoolInvitationCode = res.data.schoolInvitationCode
      ruleForm.value.logo = res.data.logo
      if (formCode.value.schoolInvitationCode) {
        // this.saveCode = true
        // this.saveType = false
      }
    }
  })
}
isInformation()

function saveInfo() {
  delete ruleForm.value.modifyTime
  delete ruleForm.value.createTime
  saveInvite(ruleForm.value).then((res) => {
    if (res.status == 0) {
      useUserStore.changePicUrl(ruleForm.value.logo)
      useUserStore.changeNickName(ruleForm.value.name)
      ElMessage.success('恭喜你，保存成功！')
    } else {
      ElMessage.error('保存失败！')
    }
  })
}
</script>
<style lang="scss" >
.el-upload {
  display: block !important;
}
.avatar-uploader-icon {
  display: block;
}
.el-input__wrapper {
  padding: 0 !important;
}
.information {
  .el-select:hover .el-input__inner {
    border-color: #e6e6e6 !important;
  }
  .el-input__inner {
    height: 40px;
    line-height: 40px;
    background: #f9f9f9;
    border: 1px solid #e6e6e6;
    border-radius: 0;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #666;
    padding: 1px 11px;
  }

  .el-textarea__inner {
    height: 120px;
    width: 317px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #666;
    line-height: 26px;
    background: #f9f9f9;
  }
}
.picture {
  .el-icon-plus:before {
    line-height: 130px;
  }
}
</style>
<style lang="scss" scoped>
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 130px;
  height: 130px;
}
.avatar {
  width: 130px;
  height: 130px;
  display: block;
}
.invite_input {
  height: 50px;
  background: #f9f9f9;
  line-height: 50px;
  .demo-input-suffix {
    padding-left: 20px;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 50px;
    .el-input {
      width: 41%;
    }
    span {
      font-size: 14px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ed7227;
      line-height: 50px;
      margin-left: 68px;
      cursor: pointer;
    }
  }
}
.picture {
  margin-top: 20px;
  text-align: center;
  .el-button {
    width: 88px;
    height: 30px;
    border: 1px solid #e6e6e6;
    border-radius: 0;
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #666666;
    line-height: 28px;
    margin-top: 12px;
    padding: 0;
  }
  .avatar-uploader .el-upload .el-icon-plus {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}
.edit {
  margin-top: 20px;
  img {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
  span {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #333333;
    line-height: 16px;
    cursor: pointer;
  }
}

.submit {
  width: 112px;
  height: 34px;
  line-height: 34px;
  border: 0;
  border-radius: 0;
  padding: 0;
  background: #ed7227;
  font-size: 14px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
.el-button--primary:active {
  background: #ed7227;
  border-color: #ed7227;
  color: #fff;
}
</style>
