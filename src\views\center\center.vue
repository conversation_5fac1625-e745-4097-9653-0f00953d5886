<template>
  <div>
    <div class="bg">
      <div class="main">
        <div class="maincontent">
          <el-row :gutter="10">
            <el-col :span="24">
              <div class="grid-content bg-purple">
                <div class="listNav">
                  <div class="main">
                    <span>首页</span>
                    <span> > </span>
                    <span>个人中心</span>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col
              class="minhei"
              :span="24"
              style="
                width: 1160px;
                padding: 30px 50px 0 50px;
                background-color: #ffffff;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
              "
            >
              <div class="grid-content bg-purple">
                <div class="touxiang">
                  <div class="imgContent">
                    <img
                      v-if="useUserStore.getPicUrl"
                      :src="useUserStore.getPicUrl"
                      alt
                    />
                    <img v-else src="../../assets/touxiang1.jpg" alt="" />
                  </div>
                  <div class="teaNickname" v-if="useUserStore.nickname">
                    {{ useUserStore.nickname }}
                  </div>
                  <div class="teaNickname" v-else>教师职称</div>
                </div>
                <el-row>
                  <el-col
                    :span="4"
                    style="
                      padding: 0px 20px 0 0;
                      margin-top: 20px;
                      min-height: 448px;
                      border-right: 1px solid #e6e6e6;
                    "
                  >
                    <router-link class="navTrain" to="/center/jiaoyan" tag="div"
                      >我的教研室</router-link
                    >
                    <router-link
                      class="navTrain"
                      to="/center/mytextbook"
                      tag="div"
                      >我的教材</router-link
                    >
                  </el-col>
                  <el-col
                    :span="20"
                    style="padding: 0 0 0 30px; margin-top: 20px"
                  >
                    <router-view></router-view>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { userStore } from '@/stores/user.js'
const useUserStore = userStore()
</script>
<style lang="scss" >
.invite_input {
  .el-input {
    .el-input__inner {
      height: 34px;
      width: 350px;
      line-height: 34px;
      background-color: #eeeeee;
      border: 0;
    }
  }
}
.information {
  .el-form-item__label {
    padding-right: 20px !important;
  }
  .el-form-item {
    margin-bottom: 15px !important;
    .el-input {
      width: 317px;
    }
    .el-input--suffix {
      width: 317px;
    }
  }
}
</style>
<style lang="scss" scoped>
.minhei {
  min-height: 738px !important;
}
.listNav {
  span {
    font-size: 14px;
    color: rgba(102, 102, 102, 1);
    line-height: 42px;
    margin-right: 3px;
  }
  :nth-child(3) {
    color: #ed7227;
  }
}
.bg {
  background: #f7f7f7;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-top: 0px;
}
.maincontent {
}
.touxiang {
  overflow: hidden;
  height: 108px;
  font-size: 0;
  border-bottom: 1px solid #e6e6e6;
  .imgContent {
    float: left;
    height: 76px;
    width: 76px;
    border: 1px solid #e6e6e6;
    margin-left: 30px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .teaNickname {
    float: left;
    margin: 27px 0 0 20px;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #222222;
    line-height: 16px;
  }
}
.separate {
  height: 10px;
  background-color: rgba(248, 248, 248, 1);
}
.navTrain {
  text-decoration: none;
  display: block;
  font-size: 14px;
  width: 160px;
  height: 40px;
  line-height: 40px;
  color: #666666;
  text-align: center;
  font-weight: 400;
  font-family: Source Han Sans CN;
  margin-bottom: 10px;
  i {
    margin-right: 12px;
    line-height: 46px;
  }
  &:hover {
    background-color: #ed7227;
    border-radius: 4px;
    color: rgba(255, 255, 255, 1);
    cursor: pointer;
  }
}
.router-link-active {
  background-color: #ed7227;
  border-radius: 4px;
  color: rgba(255, 255, 255, 1);
}
.maincontent .el-row {
  margin: 0 !important;
}
.maincontent .main {
  margin-left: 0px;
  width: 810px;
}
.maincontent .main span {
  font-size: 12px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  line-height: 30px;
}
</style>
